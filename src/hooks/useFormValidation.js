import { useState, useCallback } from 'react';
import { createValidationSchema } from '../components/form/validation/validationRules';

/**
 * Hook for dynamic form validation
 * @param {Array} fields - Field definitions with validation rules
 * @returns {Object} - Validation utilities
 */
const useFormValidation = (fields) => {
  const [dynamicFields, setDynamicFields] = useState(fields);
  const [errors, setErrors] = useState({});

  // Create validation schema from current fields
  const validationSchema = createValidationSchema(dynamicFields);

  /**
   * Validate a single field value
   * @param {String} fieldName - Name of the field to validate
   * @param {any} value - Value to validate
   * @returns {String|null} - Error message or null if valid
   */
  const validateField = useCallback(
    async (fieldName, value) => {
      try {
        await validationSchema.validateAt(fieldName, { [fieldName]: value });
        setErrors((prev) => ({ ...prev, [fieldName]: null }));
        return null;
      } catch (error) {
        const errorMessage = error.message;
        setErrors((prev) => ({ ...prev, [fieldName]: errorMessage }));
        return errorMessage;
      }
    },
    [validationSchema]
  );

  /**
   * Validate all form values
   * @param {Object} values - Form values
   * @returns {Object} - Object with isValid flag and errors
   */
  const validateForm = useCallback(
    async (values) => {
      try {
        await validationSchema.validate(values, { abortEarly: false });
        setErrors({});
        return { isValid: true, errors: {} };
      } catch (error) {
        const validationErrors = {};
        if (error.inner) {
          error.inner.forEach((err) => {
            validationErrors[err.path] = err.message;
          });
        }
        setErrors(validationErrors);
        return { isValid: false, errors: validationErrors };
      }
    },
    [validationSchema]
  );

  /**
   * Update field validation rules dynamically
   * @param {String} fieldName - Name of the field to update
   * @param {Array} validations - New validation rules
   */
  const updateFieldValidation = useCallback((fieldName, validations) => {
    setDynamicFields((prev) =>
      prev.map((field) => (field.name === fieldName ? { ...field, validations } : field))
    );
  }, []);

  /**
   * Add a conditional validation rule to a field
   * @param {String} fieldName - Name of the field to add validation to
   * @param {Function} condition - Condition function that returns true when validation should apply
   * @param {Object} validation - Validation rule to add
   */
  const addConditionalValidation = useCallback(
    (fieldName, condition, validation) => {
      const field = dynamicFields.find((f) => f.name === fieldName);
      if (!field) return;

      const customValidation = {
        type: 'test',
        params: {
          test: (value, context) => {
            if (!condition(context.parent)) return true;

            // Apply the validation logic
            switch (validation.type) {
              case 'required':
                return !!value;
              case 'min':
                return value && value.length >= validation.params.value;
              case 'max':
                return !value || value.length <= validation.params.value;
              case 'matches':
                return !value || new RegExp(validation.params.regex).test(value);
              case 'oneOf':
                return !value || validation.params.values.includes(value);
              default:
                return true;
            }
          },
        },
        message: validation.message,
      };

      updateFieldValidation(fieldName, [...field.validations, customValidation]);
    },
    [dynamicFields, updateFieldValidation]
  );

  return {
    errors,
    validateField,
    validateForm,
    updateFieldValidation,
    addConditionalValidation,
  };
};

export default useFormValidation;
