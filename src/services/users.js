import axios from 'axios';
import { apiService } from './base';

// API base URLs
const API = {
  baseAPI: process.env.REACT_APP_VG_VOICE_BE_HOST,
};

/**
 * Google authentication API call
 * Exchange Google token for a backend authentication token
 * @param {Object} params - Google auth data including id_token
 * @returns {Promise} API response with backend authentication token
 */
export const googleLogin = (params) => {
  // Pass a special option to return the full response including headers
  return axios.post(`${API.baseAPI}/voice/api/v1/users/google_authorization`, params, {
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      dcid: '147',
    },
    // Return full response so we can access headers
    transformResponse: [(data) => data],
  });
};

/**
 * Get current user profile information
 * @returns {Promise} API response with user profile data
 */
export const getUserProfile = () => {
  return apiService.get('voice/api/v1/users/current_user_profile');
};

/**
 * Get current authenticated user's profile
 * This endpoint is used to validate the session and get user data
 * @returns {Promise} API response with complete user profile data
 */
// export const getCurrentUserProfile = () => {
//   return apiService.get('voice/api/v1/users/current_user_profile');
// };

/**
 * Log out the current user
 * @returns {Promise} API response
 */
export const userLogout = () => {
  return apiService.delete('voice/api/v1/users/logout');
};
