import { apiService } from './base';

const VG_VOICE_BE_HOST = process.env.REACT_APP_VG_VOICE_BE_HOST;

// Helper to get full API URL
const apiUrl = (path) => `${VG_VOICE_BE_HOST}${path}`;

/**
 * List All Questionnaires
 * GET /voice/api/v1/questionnaires
 * @param {Object} params { skip, limit, project_id }
 * @returns {Promise} - Promise with the response data
 */
export const listQuestionnaires = (params = {}) => {
  return apiService.get(apiUrl('/voice/api/v1/questionnaires'), { params });
};

/**
 * Get Questionnaire by ID
 * GET /voice/api/v1/questionnaires/{questionnaire_id}
 * @param {number} questionnaireId
 * @returns {Promise} - Promise with the response data
 */
export const getQuestionnaireById = (questionnaireId) => {
  return apiService.get(apiUrl(`/voice/api/v1/questionnaires/${questionnaireId}`));
};

/**
 * Create Questionnaire
 * POST /voice/api/v1/questionnaires
 * @param {Object} data { project_id, question_text, question_type, options, status, order_no }
 * @returns {Promise} - Promise with the response data
 */
export const createQuestionnaire = (data) => {
  return apiService.post(apiUrl('/voice/api/v1/questionnaires'), data);
};

/**
 * Update Questionnaire
 * PUT /voice/api/v1/questionnaires/{questionnaire_id}
 * @param {number} questionnaireId
 * @param {Object} data
 * @returns {Promise} - Promise with the response data
 */
export const updateQuestionnaire = (questionnaireId, data) => {
  return apiService.put(apiUrl(`/voice/api/v1/questionnaires/${questionnaireId}`), data);
};

/**
 * Delete Questionnaire
 * DELETE /voice/api/v1/questionnaires/{questionnaire_id}
 * @param {number} questionnaireId
 * @returns {Promise} - Promise with the response data
 */
export const deleteQuestionnaire = (questionnaireId) => {
  return apiService.delete(apiUrl(`/voice/api/v1/questionnaires/${questionnaireId}`));
};

/**
 * Get all versions of a questionnaire
 * GET /voice/api/v1/questionnaires/{questionnaire_id}/versions
 * @param {number} questionnaireId - Questionnaire ID
 * @returns {Promise} - Promise with the response data
 */
export const getQuestionnaireVersions = (questionnaireId) => {
  return apiService.get(apiUrl(`/voice/api/v1/questionnaires/${questionnaireId}/versions`));
};

/**
 * Create a new version of a questionnaire
 * POST /voice/api/v1/questionnaires/{questionnaire_id}/versions
 * @param {number} questionnaireId - Questionnaire ID
 * @param {Object} data - Version data
 * @returns {Promise} - Promise with the response data
 */
export const createQuestionnaireVersion = (questionnaireId, data) => {
  return apiService.post(apiUrl(`/voice/api/v1/questionnaires/${questionnaireId}/versions`), data);
};

/**
 * Update a questionnaire version
 * PUT /voice/api/v1/questionnaires/versions/{version_id}
 * @param {number} versionId - Version ID
 * @param {Object} data - Updated version data
 * @returns {Promise} - Promise with the response data
 */
export const updateQuestionnaireVersion = (versionId, data) => {
  return apiService.put(apiUrl(`/voice/api/v1/questionnaires/versions/${versionId}`), data);
};
