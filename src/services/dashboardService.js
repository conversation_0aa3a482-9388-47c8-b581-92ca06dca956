import { apiService } from './base';

const VG_VOICE_BE_HOST = process.env.REACT_APP_VG_VOICE_BE_HOST;

// Helper to get full API URL
const apiUrl = (path) => `${VG_VOICE_BE_HOST}${path}`;

/**
 * Get dashboard stats
 * @param {string} period - Time period filter (e.g. 'today', 'week', 'month')
 * @returns {Promise} - Promise with the response data
 */
// export const getStats = (period) => {
//   return apiService.get(apiUrl('/voice/api/v1/dashboard/stats'), { params: { period } });
// };

/**
 * Get dashboard trend data
 * @param {string} period - Time period filter (e.g. 'today', 'week', 'month')
 * @returns {Promise} - Promise with the response data
 */
// export const getTrends = (period) => {
//   return apiService.get(apiUrl('/voice/api/v1/dashboard/trends'), { params: { period } });
// };

/**
 * Get call statistics for a project
 * @param {string} projectId - The project ID
 * @param {number} interval - Number of days to look back (default: 7)
 * @returns {Promise} - Promise with the response data containing total_calls, completed_calls, avg_call_duration, success_rate
 */
export const getCallStatistics = (projectId, interval = 7) => {
  return apiService.get(apiUrl('/voice/api/v1/call-statistics'), {
    params: {
      project_id: projectId,
      interval,
    },
  });
};

/**
 * Get recent calls for dashboard
 * @param {Object} params - Query parameters (e.g. limit, status)
 * @returns {Promise} - Promise with the response data
 */
export const getRecentCalls = (params = {}) => {
  return apiService.get(apiUrl('/voice/api/v1/calls'), { params });
};

/**
 * Get call statistics by filter
 * @param {Object} params - Filter parameters
 * @returns {Promise} - Promise with the response data
 */
// export const getCallStats = (params = {}) => {
//   return apiService.get(apiUrl('/voice/api/v1/dashboard/call-stats'), { params });
// };

/**
 * Get call status counts by date for a specific project
 * @param {string} projectId - The project ID
 * @returns {Promise} - Promise with the response data containing dates with call counts
 */
export const getCallStatusCounts = (projectId, interval = 7) => {
  return apiService.get(apiUrl('/voice/api/v1/call-status-count'), {
    params: {
      project_id: projectId,
      interval,
    },
  });
};
