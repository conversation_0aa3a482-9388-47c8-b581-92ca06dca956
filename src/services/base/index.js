import axios from 'axios';
import { getUserData, removeUser, getCoordinates } from '../../utils/localStorage';

// Mock data for development and fallback
const MOCK_DATA = {
  projects: [
    {
      id: '1',
      title: 'Charan - Testing',
      status: 'Active',
      language: 'Hindi',
      region: 'Nashik',
      created_at: '2023-05-15T10:30:00Z',
      updated_at: '2023-05-20T14:45:00Z',
    },
    {
      id: '2',
      title: 'Voice Bot Demo',
      status: 'Active',
      language: 'English',
      region: 'Bangalore',
      created_at: '2023-06-01T09:15:00Z',
      updated_at: '2023-06-10T16:20:00Z',
    },
  ],
};

// API base URLs based on environment
const API = {
  baseAPI: process.env.REACT_APP_VG_VOICE_BE_HOST,
};

const serviceConfig = {
  timeout: 45000,
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  },
  crossDomain: true,
};

/**
 * Create and configure an axios instance with interceptors for API calls
 * @param {string} baseURL - Base URL for the service
 * @param {Object} options - Additional options
 * @returns {Object} Configured axios instance
 */
const getServiceInstance = (baseURL, options = {}) => {
  // Map to track pending requests (for future implementation of request cancellation)
  // eslint-disable-next-line no-unused-vars
  const pendingRequests = new Map();

  // Create axios instance with config
  const serviceInstance = axios.create({
    ...serviceConfig,
    baseURL,
  });

  // Request interceptor for API calls
  serviceInstance.interceptors.request.use(
    async (config) => {
      try {
        // Get user data from localStorage
        const userData = getUserData();

        // Add auth token to headers if available
        if (userData && userData.authentication_token) {
          config.headers['Authorization'] = `Bearer ${userData.authentication_token}`;
        } else {
          console.warn('No auth token available for request to:', config.url);
        }

        // Add dcid header - required for API calls
        config.headers['dcid'] = '147';

        // Add coordinates to headers
        try {
          const coords = await getCoordinates();
          if (coords.latitude && coords.longitude) {
            config.headers['x-latitude'] = coords.latitude;
            config.headers['x-longitude'] = coords.longitude;
          }
        } catch (geoError) {
          console.warn('Could not get coordinates:', geoError);
        }

        return config;
      } catch (error) {
        console.error('Error in request interceptor:', error);
        return config;
      }
    },
    (error) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  serviceInstance.interceptors.response.use(
    (response) => {
      // Return just the data portion of the response
      return response.data;
    },
    (error) => {
      // Handle request timeout
      if (error.code === 'ECONNABORTED') {
        const timeoutError = {
          status: 504,
          message: 'Request Timed Out',
        };
        return Promise.reject(timeoutError);
      }

      // Handle authentication error - redirect to login
      if (
        error.response &&
        error.response.status === 401 &&
        error.config &&
        error.config.url !== 'login'
      ) {
        removeUser();
        window.location.href = '/login';
        return Promise.reject(error);
      }

      // Use mock data for development if API is unavailable
      if (
        process.env.NODE_ENV === 'development' ||
        process.env.REACT_APP_USE_MOCK_DATA === 'true'
      ) {
        console.warn('Using mock data for:', error.config.url);

        // Check if the request URL contains specific endpoints and return mock data
        if (error.config.url.includes('/projects')) {
          if (error.config.url.includes('/projects/') && error.config.method === 'get') {
            // Single project request
            const projectId = error.config.url.split('/').pop();
            const project = MOCK_DATA.projects.find((p) => p.id === projectId);
            if (project) {
              console.log('Returning mock project:', project);
              return { data: project };
            }
          } else if (error.config.method === 'get') {
            // List projects request
            console.log('Returning mock projects list');
            return {
              items: MOCK_DATA.projects,
              total: MOCK_DATA.projects.length,
            };
          }
        }
      }

      return Promise.reject(error);
    }
  );

  return serviceInstance;
};

// Export configured service instances
export const apiService = getServiceInstance(API.baseAPI);
