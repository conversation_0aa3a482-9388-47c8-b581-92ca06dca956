import { apiService } from './base';

const VG_VOICE_BE_HOST = process.env.REACT_APP_VG_VOICE_BE_HOST;

// Helper to get full API URL
const apiUrl = (path) => `${VG_VOICE_BE_HOST}${path}`;

/**
 * Trigger a call
 * POST /voice/api/v1/calls/trigger
 * @param {Object} data { project_id, persona_version_id, phone_number, variables }
 * @returns {Promise} - Promise with the response data
 */
export const triggerCall = (data) => {
  return apiService.post(apiUrl('/voice/api/v1/calls/trigger'), data);
};

export default {
  triggerCall,
};
