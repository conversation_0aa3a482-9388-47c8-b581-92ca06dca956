import React, { useEffect, useState, useCallback } from 'react';
import DataTable from '../../components/DataTable/DataTable';
import Button from '../../components/Button';
import { listProjects, createProject, updateProject } from '../../services/projectsService';
import ProjectFormModal from './ProjectFormModal';
import { useNavigate } from 'react-router-dom';

const columnsBase = [
  { headerName: 'Title', field: 'title' },
  { headerName: 'Language', field: 'language' },
  { headerName: 'Region', field: 'region' },
  { headerName: 'Status', field: 'status' },
];

const Projects = () => {
  const navigate = useNavigate();
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);

  // Modal state
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create' or 'edit'
  const [modalInitialValues, setModalInitialValues] = useState({
    title: '',
    language: '',
    region: '',
    status: 'Active',
  });
  const [actionLoading, setActionLoading] = useState(false);
  const [actionError, setActionError] = useState(null);

  const fetchProjects = useCallback(async (currentPage = 0, currentLimit = 10) => {
    setLoading(true);
    setError(null);
    try {
      const res = await listProjects({
        skip: currentPage * currentLimit,
        limit: currentLimit,
      });
      setProjects(res.items || []);
      setTotalCount(res.total ?? 0);
    } catch (err) {
      console.error('Error loading projects:', err);
      setError('Failed to load projects');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchProjects(page, rowsPerPage);
  }, [fetchProjects, page, rowsPerPage]);

  const handlePageChange = (event, newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // CREATE
  const handleOpenCreate = () => {
    setModalMode('create');
    setModalInitialValues({ title: '', language: '', region: '', status: 'Active' });
    setModalOpen(true);
    setActionError(null);
  };

  // EDIT
  const handleOpenEdit = (project) => {
    setModalMode('edit');
    setModalInitialValues({ ...project });
    setModalOpen(true);
    setActionError(null);
  };

  // DELETE
  const handleDelete = async (projectId) => {
    if (!window.confirm('Are you sure you want to delete this project?')) return;
    setActionLoading(true);
    setActionError(null);
    try {
      await updateProject(projectId, { status: 'Archived' });
      fetchProjects(page, rowsPerPage);
    } catch (err) {
      setActionError('Failed to delete project');
    } finally {
      setActionLoading(false);
    }
  };

  // ROW CLICK - Navigate to project detail
  const handleRowClick = (row) => {
    if (row && row.id) {
      navigate(`/projects/${row.id}`);
    }
  };

  // SUBMIT (CREATE/EDIT)
  const handleModalSubmit = async (values) => {
    setActionLoading(true);
    setActionError(null);
    try {
      if (modalMode === 'edit') {
        await updateProject(values.id, values);
      } else {
        await createProject(values);
      }
      setModalOpen(false);
      fetchProjects(page, rowsPerPage);
    } catch (err) {
      setActionError('Failed to save project');
    } finally {
      setActionLoading(false);
    }
  };

  // Columns with actions
  const columns = [
    ...columnsBase,
    {
      headerName: 'Actions',
      field: 'actions',
      renderCell: ({ row }) => (
        <div style={{ display: 'flex', gap: 8 }}>
          <Button size='small' variant='outlined' onClick={() => handleOpenEdit(row)}>
            Edit
          </Button>
          <Button
            size='small'
            variant='outlined'
            color='error'
            onClick={() => handleDelete(row.id)}
            disabled={actionLoading}
          >
            Archive
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: 24,
        }}
      >
        <h2>Projects</h2>
        <Button onClick={handleOpenCreate}>+ Create Project</Button>
      </div>
      {error && <div style={{ color: 'red' }}>{error}</div>}
      {actionError && <div style={{ color: 'red' }}>{actionError}</div>}
      <DataTable
        columns={columns}
        data={projects}
        totalCount={totalCount}
        loading={loading}
        onPageChange={handlePageChange}
        onRowsPerPageChange={(newRowsPerPage) => {
          handleRowsPerPageChange({ target: { value: newRowsPerPage } });
        }}
        serverSidePagination={true}
        title="Projects"
        onSearch={(query) => {
          console.log('Search:', query);
        }}
        onRowClick={handleRowClick}
      />
      <ProjectFormModal
        open={modalOpen}
        onClose={() => setModalOpen(false)}
        onSubmit={handleModalSubmit}
        initialValues={modalInitialValues}
        mode={modalMode}
      />
    </div>
  );
};
export default Projects;
