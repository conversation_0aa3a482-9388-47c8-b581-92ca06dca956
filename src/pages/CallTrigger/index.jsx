import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  CircularProgress,
  useTheme,
  Divider,
  Alert,
  Grid,
  Card,
  CardContent,
  Stepper,
  Step,
  StepLabel,
  IconButton,
  Tooltip,
  InputAdornment,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  ListItemSecondaryAction,
  Switch,
  FormControlLabel,
  Radio,
  RadioGroup,
} from '@mui/material';
import {
  Phone as PhoneIcon,
  Send as SendIcon,
  Info as InfoIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  Message as MessageIcon,
  Help as HelpIcon,
  Language as LanguageIcon,
  RecordVoiceOver as VoiceIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useProject } from '../../contexts/ProjectContext';
import { listPersonas } from '../../services/personaService';
import { listPrompts } from '../../services/promptService';
import { triggerCall } from '../../services/callService';

const CallTrigger = () => {
  const theme = useTheme();
  const { selectedProject } = useProject();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [personas, setPersonas] = useState([]);
  const [selectedPersona, setSelectedPersona] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [variables, setVariables] = useState({});
  const [promptText, setPromptText] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeStep, setActiveStep] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [selectedPersonaVersion, setSelectedPersonaVersion] = useState(null);

  const steps = ['Choose Persona', 'Enter Details', 'Review & Trigger'];

  const fetchPersonas = useCallback(async () => {
    try {
      const params = {};
      if (selectedProject?.id) {
        params.project_id = selectedProject.id;
      }
      const res = await listPersonas(params);
      // Sort personas to put default first
      const sortedPersonas = (res.items || []).sort((a, b) => {
        if (a.is_default && !b.is_default) return -1;
        if (!a.is_default && b.is_default) return 1;
        return 0;
      });
      setPersonas(sortedPersonas);

      // Select default persona if available
      const defaultPersona = sortedPersonas.find((p) => p.is_default);
      if (defaultPersona) {
        setSelectedPersona(defaultPersona.id);
        await fetchPrompt(defaultPersona.id);
      }
    } catch (err) {
      console.error('Error loading personas:', err);
      setError('Failed to load personas');
    }
  }, [selectedProject]);

  const fetchPrompt = async (personaId) => {
    try {
      const params = {
        persona_id: personaId,
        type: 'conversational',
      };
      const res = await listPrompts(params);
      if (res.items && res.items.length > 0) {
        const prompt = res.items[0];
        setPromptText(prompt.current_version?.prompt_text || '');
        const initialVariables = {};
        if (prompt.current_version?.variables) {
          prompt.current_version.variables.forEach((variable) => {
            initialVariables[variable] = '';
          });
        }
        setVariables(initialVariables);
      }
    } catch (err) {
      console.error('Error loading prompt:', err);
      setError('Failed to load prompt');
    }
  };

  useEffect(() => {
    fetchPersonas();
  }, [fetchPersonas]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleVariableChange = (variable, value) => {
    setVariables((prev) => ({
      ...prev,
      [variable]: value,
    }));
  };

  const handleNext = () => {
    setActiveStep((prevStep) => prevStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Validate phone number
      const phoneRegex = /^[0-9]{10}$/;
      if (!phoneRegex.test(phoneNumber)) {
        throw new Error('Please enter a valid 10-digit phone number');
      }

      const selectedPersonaData = personas.find((p) => p.id === selectedPersona);
      if (!selectedPersonaData || !selectedPersonaData.current_version) {
        throw new Error('No persona version selected');
      }

      const payload = {
        project_id: selectedProject.id,
        persona_version_id: selectedPersonaData.current_version.id,
        phone_number: phoneNumber,
        variables: variables,
      };

      await triggerCall(payload);
      setSuccess('Call triggered successfully!');
      // Reset form
      setPhoneNumber('');
      setVariables({});
      setActiveStep(0);
    } catch (err) {
      console.error('Error triggering call:', err);
      setError(err.message || err.response?.data?.message || 'Failed to trigger call');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Card variant='outlined' sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant='h6'>Choose Persona</Typography>
              </Box>
              <List>
                {personas.map((persona) => (
                  <ListItem
                    key={persona.id}
                    button
                    selected={selectedPersona === persona.id}
                    onClick={async () => {
                      setSelectedPersona(persona.id);
                      await fetchPrompt(persona.id);
                      handleNext();
                    }}
                    sx={{
                      mb: 2,
                      borderRadius: 1,
                      border: '1px solid',
                      borderColor: selectedPersona === persona.id ? 'primary.main' : 'divider',
                    }}
                  >
                    <ListItemAvatar>
                      <Avatar>
                        <VoiceIcon />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant='body1'>{persona.name}</Typography>
                          {persona.is_default && (
                            <Chip label='Default' size='small' color='primary' variant='outlined' />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                          <Chip
                            icon={<LanguageIcon />}
                            label={persona.current_version?.language || persona.language}
                            size='small'
                          />
                          <Chip
                            icon={<VoiceIcon />}
                            label={persona.current_version?.voice_type || persona.voice_type}
                            size='small'
                          />
                        </Box>
                      }
                    />
                    <ListItemSecondaryAction>
                      <IconButton edge='end' onClick={() => setIsRecording(!isRecording)}>
                        {isRecording ? <StopIcon color='error' /> : <PlayIcon color='primary' />}
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        );

      case 1:
        return (
          <Card variant='outlined' sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PhoneIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant='h6'>Enter Details</Typography>
              </Box>
              <Grid container spacing={3} direction='column'>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label='Phone Number'
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value.replace(/[^0-9]/g, ''))}
                    required
                    error={phoneNumber.length > 0 && phoneNumber.length !== 10}
                    helperText={
                      phoneNumber.length > 0 && phoneNumber.length !== 10
                        ? 'Please enter a valid 10-digit phone number'
                        : ''
                    }
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position='start'>
                          <PhoneIcon color='action' />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>

                {Object.keys(variables).length > 0 && (
                  <>
                    <Grid item xs={12}>
                      <Divider sx={{ my: 2 }} />
                      <Typography variant='subtitle1' gutterBottom>
                        Variables
                      </Typography>
                    </Grid>
                    {Object.keys(variables).map((variable) => (
                      <Grid item xs={12} key={variable}>
                        <TextField
                          fullWidth
                          label={variable.replace(/_/g, ' ')}
                          value={variables[variable]}
                          onChange={(e) => handleVariableChange(variable, e.target.value)}
                          required
                          helperText={`Enter the ${variable.replace(/_/g, ' ')}`}
                        />
                      </Grid>
                    ))}
                  </>
                )}
              </Grid>
            </CardContent>
          </Card>
        );

      case 2:
        return (
          <Card variant='outlined' sx={{ mb: 3 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <MessageIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant='h6'>Review & Trigger</Typography>
              </Box>

              <Grid container spacing={3} direction='column'>
                <Grid item xs={12}>
                  <Typography variant='subtitle2' color='text.secondary' gutterBottom>
                    Project
                  </Typography>
                  <Typography variant='body1'>{selectedProject?.title}</Typography>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant='subtitle2' color='text.secondary' gutterBottom>
                    Persona
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Avatar>
                      <VoiceIcon />
                    </Avatar>
                    <Typography variant='body1'>
                      {personas.find((p) => p.id === selectedPersona)?.name}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant='subtitle2' color='text.secondary' gutterBottom>
                    Phone Number
                  </Typography>
                  <Typography variant='body1'>{phoneNumber}</Typography>
                </Grid>

                {Object.keys(variables).length > 0 && (
                  <Grid item xs={12}>
                    <Typography variant='subtitle2' color='text.secondary' gutterBottom>
                      Variables
                    </Typography>
                    <Grid container spacing={2} direction='column'>
                      {Object.entries(variables).map(([key, value]) => (
                        <Grid item xs={12} key={key}>
                          <Paper variant='outlined' sx={{ p: 2 }}>
                            <Typography variant='subtitle2' color='text.secondary'>
                              {key.replace(/_/g, ' ')}
                            </Typography>
                            <Typography variant='body1'>{value}</Typography>
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  </Grid>
                )}

                <Grid item xs={12}>
                  <Typography variant='subtitle2' color='text.secondary' gutterBottom>
                    Prompt Text
                  </Typography>
                  <Paper variant='outlined' sx={{ p: 2, bgcolor: theme.palette.grey[50] }}>
                    <Typography variant='body1' sx={{ whiteSpace: 'pre-wrap' }}>
                      {promptText}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        );

      default:
        return null;
    }
  };

  return (
    <Box sx={{ width: '100%', py: 4, px: 3 }}>
      <Typography variant='h4' gutterBottom sx={{ fontWeight: 600 }}>
        Call Trigger
      </Typography>
      <Typography variant='body1' color='text.secondary' sx={{ mb: 4 }}>
        Trigger individual or bulk calls with selected personas
      </Typography>

      <Paper sx={{ width: '100%', mb: 4 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label='Individual Trigger' />
          <Tab label='Bulk Trigger' />
        </Tabs>
      </Paper>

      {activeTab === 0 && (
        <Paper sx={{ p: 4, maxWidth: 800, mx: 'auto' }}>
          {error && (
            <Alert severity='error' sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert severity='success' sx={{ mb: 3 }}>
              {success}
            </Alert>
          )}

          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          <Box>
            {renderStepContent(activeStep)}

            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button disabled={activeStep === 0} onClick={handleBack} variant='outlined'>
                Back
              </Button>

              {activeStep === steps.length - 1 ? (
                <Button
                  onClick={handleSubmit}
                  variant='contained'
                  color='primary'
                  size='large'
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                >
                  {loading ? 'Triggering Call...' : 'Trigger Call'}
                </Button>
              ) : (
                <Button
                  variant='contained'
                  onClick={handleNext}
                  disabled={
                    (activeStep === 0 && !selectedPersona) ||
                    (activeStep === 1 && (!phoneNumber || Object.values(variables).some((v) => !v)))
                  }
                >
                  Next
                </Button>
              )}
            </Box>
          </Box>
        </Paper>
      )}

      {activeTab === 1 && (
        <Box sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant='h6' color='text.secondary'>
            Bulk Trigger functionality coming soon...
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default CallTrigger;
