import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Avatar,
  Typography,
  Paper,
  Button,
  CircularProgress,
  Card,
  CardContent,
  MenuItem,
  Select,
  FormControl,
  IconButton,
  Chip,
  Tooltip,
  useTheme,
  Alert,
} from '@mui/material';
import { useProject } from '../../contexts/ProjectContext';
import { AddCall, Refresh, AccessTime, Speed } from '@mui/icons-material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Line,
} from 'recharts';
import PhoneIcon from '@mui/icons-material/Phone';
import DoneIcon from '@mui/icons-material/Done';
import { useNavigate } from 'react-router-dom';
import { format, subDays, formatDistance, parseISO } from 'date-fns';
import {
  getRecentCalls,
  getCallStatistics,
  getCallStatusCounts,
} from '../../services/dashboardService';

/**
 * Dashboard page component
 * Displays project and call statistics, including charts and graphs
 *
 * @returns {JSX.Element} Dashboard component
 */
const Dashboard = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalCalls: 0,
    completedCalls: 0,
    avgCallDuration: 0,
    successRate: 0,
  });
  const { selectedProjectId } = useProject();
  const [trendData, setTrendData] = useState([]);
  const [recentCalls, setRecentCalls] = useState([]);
  const [refreshing, setRefreshing] = useState(false);
  const [timeFilter, setTimeFilter] = useState('today');
  const [error, setError] = useState(null);
  const navigate = useNavigate();
  const [callsPage, setCallsPage] = useState(0);
  const [callsPerPage, setCallsPerPage] = useState(5);
  const [recentCallsTotal, setRecentCallsTotal] = useState(0);

  useEffect(() => {
    fetchDashboardData();
  }, [timeFilter, selectedProjectId, callsPage, callsPerPage]);

  const getDaysFromFilter = (filter) => {
    switch (filter) {
      case 'today':
        return 1;
      case '7days':
        return 7;
      case '30days':
        return 30;
      case '90days':
        return 90;
      case '365days':
        return 365;
      default:
        return 1;
    }
  };
  const fetchDashboardData = () => {
    setLoading(true);
    // Fetch call statistics for the selected project
    getCallStatistics(selectedProjectId, getDaysFromFilter(timeFilter))
      .then((data) => {
        setStats({
          totalCalls: data.total_calls || 0,
          completedCalls: data.completed_calls || 0,
          avgCallDuration: data.avg_call_duration || 0,
          successRate: data.success_rate || 0,
        });
      })
      .catch((error) => {
        setError(error);
        setStats({
          totalCalls: 0,
          completedCalls: 0,
          avgCallDuration: 0,
          successRate: 0,
        });
      });

    // Fetch  status counts for graph
    getCallStatusCounts(selectedProjectId, getDaysFromFilter(timeFilter))
      .then((data) => {
        if (data && data.dates) {
          const formattedData = Object.entries(data.dates).map(([date, counts]) => {
            const successRate =
              counts.total_calls > 0 ? (counts.completed_calls / counts.total_calls) * 100 : 0;

            const dateObj = new Date(date);
            const dayOfWeek = dateObj.toLocaleDateString('en-US', { weekday: 'short' });
            return {
              date: dayOfWeek,
              totalCalls: counts.total_calls,
              completedCalls: counts.completed_calls,
              failedCalls: counts.failed_calls,
              inProgressCalls: counts.in_progress_calls,
              successRate: successRate,
            };
          });
          setTrendData(formattedData);
        } else {
          setTrendData([]);
        }
      })
      .catch((error) => {
        setError(error);
        setTrendData(generateMockTrendData());
      });

    // Fetch recent calls
    getRecentCalls({
      limit: callsPerPage,
      skip: callsPage * callsPerPage,
      interval: getDaysFromFilter(timeFilter),
      project_id: selectedProjectId,
    })
      .then((data) => {
        setRecentCalls(data.items || []);
        // Store total count if available in the response
        if (data.total) {
          setRecentCallsTotal(data.total);
        }
      })
      .catch((error) => {
        setError(error);
        setRecentCalls(generateMockRecentCalls());
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const generateMockTrendData = () => {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days.map((date) => {
      const totalCalls = Math.floor(Math.random() * 150) + 50;
      const completedCalls = Math.floor(Math.random() * totalCalls);
      return {
        date,
        totalCalls,
        completedCalls,
        successRate: Math.floor(Math.random() * 30) + 70,
      };
    });
  };

  const generateMockRecentCalls = () => {
    return Array.from({ length: 5 }, (_, index) => ({
      id: index + 1,
      phone_number: `+91 ${Math.floor(Math.random() * 9000000000) + 1000000000}`,
      status: ['completed', 'failed', 'in_progress'][Math.floor(Math.random() * 3)],
      duration: Math.floor(Math.random() * 300) + 60,
      created_at: format(subDays(new Date(), Math.floor(Math.random() * 7)), 'yyyy-MM-dd HH:mm'),
    }));
  };

  const handleStartCall = () => {
    navigate('/call-trigger');
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDashboardData();
    setRefreshing(false);
  };

  const handleViewCall = (callId) => {
    navigate(`/call-logs/${callId}`);
  };

  const handleViewAllCalls = () => {
    navigate('/call-logs');
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      case 'in_progress':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'completed':
        return 'Completed';
      case 'failed':
        return 'Failed';
      case 'in_progress':
        return 'In Progress';
      default:
        return 'Unknown';
    }
  };

  /**
   * Format relative time in a human-readable format
   * @param {string} dateTimeString - ISO date string
   * @returns {string} - Formatted relative time (e.g., '5 min ago', '2 hours 30 min ago')
   */
  const formatRelativeTime = (dateTimeString) => {
    if (!dateTimeString) return '';

    try {
      const date =
        typeof dateTimeString === 'string' ? parseISO(dateTimeString) : new Date(dateTimeString);
      return formatDistance(date, new Date(), { addSuffix: true });
    } catch (error) {
      console.error('Error formatting date:', error);
      return '';
    }
  };

  const handleCallsPageChange = (event, newPage) => {
    if (typeof newPage === 'number') {
      setCallsPage(newPage);
    }
  };

  const handleCallsPerPageChange = (event) => {
    if (event && event.target && event.target.value) {
      const newRowsPerPage = parseInt(event.target.value, 10);
      if (!isNaN(newRowsPerPage)) {
        setCallsPerPage(newRowsPerPage);
        setCallsPage(0);
      }
    }
  };

  return (
    <Box sx={{ width: '100%', py: 2, px: 3 }}>
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box>
          {error && <Alert severity='error'>Error fetching dashboard data: {error.message}</Alert>}
          <Typography variant='h5' gutterBottom sx={{ fontWeight: 600 }}>
            Dashboard
          </Typography>
          <Typography variant='body2' color='text.secondary'>
            Overview of your calling metrics
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl
            variant='outlined'
            size='small'
            sx={{ minWidth: 120, border: '1px solid #e0e0e0', borderRadius: 1 }}
          >
            <Select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              displayEmpty
              sx={{ '& .MuiSelect-select': { py: 1 } }}
            >
              <MenuItem value='today'>Today</MenuItem>
              <MenuItem value='7days'>Last 7 Days</MenuItem>
              <MenuItem value='30days'>Last 30 Days</MenuItem>
              <MenuItem value='90days'>Last 90 Days</MenuItem>
              <MenuItem value='365days'>Last Year</MenuItem>
            </Select>
          </FormControl>
          <Tooltip title='Refresh Data'>
            <IconButton
              color='primary'
              onClick={handleRefresh}
              disabled={refreshing}
              sx={{
                backgroundColor: theme.palette.background.paper,
                border: '1px solid #e0e0e0',
                borderRadius: 1,
                '&:hover': {
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              {refreshing ? <CircularProgress size={24} /> : <Refresh />}
            </IconButton>
          </Tooltip>
          <Button
            variant='contained'
            color='success'
            startIcon={<AddCall />}
            onClick={handleStartCall}
            sx={{
              borderRadius: 1,
              textTransform: 'none',
              px: 3,
              py: 1,
              backgroundColor: '#77c64a',
            }}
          >
            Trigger Call
          </Button>
        </Box>
      </Box>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 2 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* First row: Summary Cards */}
          <Grid container spacing={2} sx={{ mb: 2, width: '100%' }}>
            <Grid item xs={12} sm={6} md={3} sx={{ flexGrow: 1 }}>
              <Card sx={{ bgcolor: '#f5f5f5', height: '100%', borderRadius: 1 }}>
                <CardContent sx={{ p: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Typography variant='subtitle2' color='text.secondary' gutterBottom>
                        Calls Triggered Today
                      </Typography>
                      <Typography variant='h4' sx={{ fontWeight: 'bold', mb: 1 }}>
                        {stats.totalCalls}
                      </Typography>
                      {/* <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                        <span>↑ 12.5%</span>
                      </Typography> */}
                    </Box>
                    <Avatar
                      sx={{ bgcolor: '#e0e0e0', color: '#757575', width: 48, height: 48, ml: 1 }}
                    >
                      <PhoneIcon fontSize='small' />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} sx={{ flexGrow: 1 }}>
              <Card sx={{ bgcolor: '#f5f5f5', height: '100%', borderRadius: 1 }}>
                <CardContent sx={{ p: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Typography variant='subtitle2' color='text.secondary' gutterBottom>
                        Calls Completed
                      </Typography>
                      <Typography variant='h4' sx={{ fontWeight: 'bold', mb: 1 }}>
                        {stats.completedCalls}
                      </Typography>
                      {/* <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center' }}>
                        <span>↑ 8.7%</span>
                      </Typography> */}
                    </Box>
                    <Avatar
                      sx={{ bgcolor: '#e0e0e0', color: '#757575', width: 48, height: 48, ml: 1 }}
                    >
                      <DoneIcon fontSize='small' />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} sx={{ flexGrow: 1 }}>
              <Card sx={{ bgcolor: '#77c64a', height: '100%', borderRadius: 1, color: 'white' }}>
                <CardContent sx={{ p: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Typography variant='subtitle2' color='rgba(255,255,255,0.8)' gutterBottom>
                        Success Rate
                      </Typography>
                      <Typography variant='h4' sx={{ fontWeight: 'bold', mb: 1, color: 'white' }}>
                        {`${stats.successRate.toFixed(1)}%`}
                      </Typography>
                      {/* <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', color: 'rgba(255,255,255,0.8)' }}>
                        <span>↑ 3.2%</span>
                      </Typography> */}
                    </Box>
                    <Avatar
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.2)',
                        color: 'white',
                        width: 48,
                        height: 48,
                        ml: 1,
                      }}
                    >
                      <Speed fontSize='small' />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3} lg={3} sx={{ flexGrow: 1 }}>
              <Card sx={{ bgcolor: '#77c64a', height: '100%', borderRadius: 1, color: 'white' }}>
                <CardContent sx={{ p: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Box sx={{ flex: 1 }}>
                      <Typography variant='subtitle2' color='rgba(255,255,255,0.8)' gutterBottom>
                        Avg. Call Duration
                      </Typography>
                      <Typography variant='h4' sx={{ fontWeight: 'bold', mb: 1, color: 'white' }}>
                        {stats.avgCallDuration > 60
                          ? `${Math.floor(stats.avgCallDuration / 60)}m ${Math.round(stats.avgCallDuration % 60)}s`
                          : `${stats.avgCallDuration.toFixed(1)}s`}
                      </Typography>
                      {/* <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', color: 'rgba(255,255,255,0.8)' }}>
                        <span>↑ 5.6%</span>
                      </Typography> */}
                    </Box>
                    <Avatar
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.2)',
                        color: 'white',
                        width: 48,
                        height: 48,
                        ml: 1,
                      }}
                    >
                      <AccessTime fontSize='small' />
                    </Avatar>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Second row: Call Trends and Recent Calls */}
          <Grid container spacing={2} sx={{ width: '100%', flexWrap: 'nowrap' }}>
            {/* Left side: Call Trends Bar Chart */}
            <Grid item sx={{ flex: '1 1 auto', minWidth: 0 }}>
              <Paper
                sx={{
                  p: 2,
                  height: '100%',
                  boxShadow: 0,
                  border: '1px solid #e0e0e0',
                  borderRadius: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  width: '100%',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 2,
                  }}
                >
                  <Box>
                    <Typography variant='h6' sx={{ fontWeight: 600 }}>
                      Weekly Performance
                    </Typography>
                    <Typography variant='body2' color='text.secondary'>
                      Calls vs Success Rate
                    </Typography>
                  </Box>
                </Box>
                <Box sx={{ flexGrow: 1, minHeight: 400, width: '100%', overflow: 'hidden' }}>
                  <ResponsiveContainer width='100%' height={400}>
                    <BarChart
                      data={trendData}
                      margin={{
                        top: 20,
                        right: 40,
                        left: 20,
                        bottom: 30,
                      }}
                    >
                      <CartesianGrid strokeDasharray='3 3' stroke='#f0f0f0' />
                      <XAxis
                        dataKey='date'
                        tick={{ fill: theme.palette.text.secondary }}
                        axisLine={{ stroke: theme.palette.divider }}
                      />
                      <YAxis
                        yAxisId='left'
                        orientation='left'
                        tick={{ fill: theme.palette.text.secondary }}
                        axisLine={{ stroke: theme.palette.divider }}
                      />
                      <YAxis
                        yAxisId='right'
                        orientation='right'
                        tick={{ fill: theme.palette.text.secondary }}
                        axisLine={{ stroke: theme.palette.divider }}
                        domain={[0, 100]}
                        unit='%'
                      />
                      <RechartsTooltip
                        contentStyle={{
                          backgroundColor: theme.palette.background.paper,
                          border: `1px solid ${theme.palette.divider}`,
                          borderRadius: 4,
                        }}
                        formatter={(value, name) => {
                          if (name === 'Success Rate') {
                            return [`${value}%`, name];
                          }
                          return [value, name];
                        }}
                      />
                      <Legend verticalAlign='top' height={36} />
                      <Bar
                        yAxisId='left'
                        dataKey='totalCalls'
                        name='Calls'
                        fill='#5470FF'
                        radius={[0, 0, 0, 0]}
                        barSize={30}
                      />
                      <Bar
                        yAxisId='left'
                        dataKey='completedCalls'
                        name='Completed Calls'
                        fill='#77c64a'
                        radius={[0, 0, 0, 0]}
                        barSize={30}
                      />
                      <Line
                        yAxisId='right'
                        type='monotone'
                        dataKey='successRate'
                        name='Success Rate'
                        stroke='#4CAF50'
                        strokeWidth={2}
                        dot={{ r: 4, fill: '#4CAF50' }}
                        activeDot={{ r: 6 }}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </Box>
              </Paper>
            </Grid>

            {/* Right side: Recent Calls List */}
            <Grid item sx={{ width: '310px', flexShrink: 0 }}>
              <Paper
                sx={{
                  p: 2,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  boxShadow: 0,
                  border: '1px solid #e0e0e0',
                  borderRadius: 1,
                  width: '100%',
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mb: 1.5,
                  }}
                >
                  <Typography variant='h6' sx={{ fontWeight: 600 }}>
                    Recent Calls
                  </Typography>
                  <Button
                    color='primary'
                    size='small'
                    onClick={handleViewAllCalls}
                    sx={{ textTransform: 'none', fontSize: '0.75rem' }}
                  >
                    View All
                  </Button>
                </Box>
                <Box sx={{ flexGrow: 1 }}>
                  {recentCalls.length > 0 ? (
                    recentCalls.map((call) => (
                      <Box
                        key={call.id}
                        sx={{
                          py: 1.5,
                          borderBottom: '1px solid #f0f0f0',
                          '&:last-child': { borderBottom: 'none' },
                          cursor: 'pointer',
                          '&:hover': { bgcolor: 'rgba(0,0,0,0.02)' },
                        }}
                        onClick={() => handleViewCall(call.id)}
                      >
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Chip
                            size='small'
                            label={getStatusText(call.status)}
                            color={getStatusColor(call.status)}
                            sx={{
                              borderRadius: 1,
                              fontWeight: 500,
                              height: '24px',
                              fontSize: '0.7rem',
                            }}
                          />
                          <Typography variant='caption' color='text.secondary'>
                            {call.created_at ? formatRelativeTime(call.created_at) : ''}
                          </Typography>
                        </Box>
                        <Typography
                          variant='body1'
                          sx={{ mt: 0.5, fontWeight: 400, fontSize: '13px' }}
                        >
                          {call.to_number}
                        </Typography>
                      </Box>
                    ))
                  ) : (
                    <Box sx={{ py: 4, textAlign: 'center' }}>
                      <Typography variant='body2' color='text.secondary'>
                        No recent calls
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </>
      )}
    </Box>
  );
};

export default Dashboard;
