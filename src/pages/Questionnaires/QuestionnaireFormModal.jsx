import React from 'react';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import { useTheme } from '@mui/material/styles';
import FormBuilder from '../../components/form/FormBuilder';

const QuestionType = {
  MULTIPLE_CHOICE: 'Multiple Choice',
  SINGLE_CHOICE: 'Single Choice',
  TEXT: 'Text',
  RATING: 'Rating',
  BOOLEAN: 'Boolean',
};

const QuestionStatus = {
  DRAFT: 'Draft',
  ACTIVE: 'Active',
  ARCHIVED: 'Archived',
  TESTING: 'Testing',
  INACTIVE: 'Inactive',
};

const getFields = (mode) => {
  if (mode === 'edit') {
    return [
      {
        name: 'status',
        label: 'Status',
        type: 'select',
        required: true,
        options: Object.entries(QuestionStatus).map(([key, value]) => ({ label: value, value })),
        gridProps: { xs: 12 },
        size: 'large',
      },
    ];
  }

  return [
    {
      name: 'question_text',
      label: 'Question Text',
      type: 'textarea',
      required: true,
      gridProps: { xs: 12 },
      size: 'large',
      rows: 4,
      multiline: true,
      minRows: 4,
      maxRows: 8,
      sx: {
        '& .MuiInputBase-root': {
          fontFamily: 'monospace',
          fontSize: '0.875rem',
        },
      },
    },
    {
      name: 'question_type',
      label: 'Type',
      type: 'select',
      required: true,
      options: Object.entries(QuestionType).map(([key, value]) => ({ label: value, value })),
      gridProps: { xs: 12 },
      size: 'large',
    },
    {
      name: 'order_no',
      label: 'Order',
      type: 'number',
      required: true,
      gridProps: { xs: 12 },
      size: 'large',
    },
    {
      name: 'status',
      label: 'Status',
      type: 'select',
      required: true,
      options: Object.entries(QuestionStatus).map(([key, value]) => ({ label: value, value })),
      gridProps: { xs: 12 },
      size: 'large',
    },
  ];
};

const QuestionnaireFormModal = ({ open, onClose, onSubmit, initialValues, mode, projectId }) => {
  const theme = useTheme();
  const fields = getFields(mode);

  const handleSubmit = (values) => {
    onSubmit({
      ...values,
      project_id: projectId,
    });
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth='sm'
      fullWidth
      PaperProps={{
        elevation: 1,
        sx: {
          borderRadius: 0,
          overflow: 'hidden',
        },
      }}
    >
      <DialogTitle
        sx={{
          bgcolor: theme.palette.primary.main,
          color: theme.palette.primary.contrastText,
          py: 2,
          fontSize: '1.25rem',
          fontWeight: 500,
        }}
      >
        {mode === 'edit' ? 'Edit Question' : 'Add New Question'}
      </DialogTitle>
      <DialogContent sx={{ p: 3, pt: 3 }}>
        <FormBuilder
          fields={fields}
          initialValues={initialValues}
          onSubmit={handleSubmit}
          formLayout={{
            fieldSpacing: 0.5,
            direction: 'column',
            variant: 'standard',
            padding: 0,
            buttonAlignment: 'space-between',
            submitButtonProps: {
              variant: 'contained',
              color: 'primary',
              size: 'large',
              sx: { px: 4 },
            },
            cancelButtonProps: {
              variant: 'outlined',
              color: 'inherit',
              size: 'large',
              sx: { px: 4 },
            },
          }}
          submitButtonText={mode === 'edit' ? 'Update' : 'Create'}
          cancelButtonText='Cancel'
          onCancel={onClose}
        />
      </DialogContent>
    </Dialog>
  );
};

export default QuestionnaireFormModal;
