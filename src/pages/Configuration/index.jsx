import React, { useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  Grid,
  Divider,
  Chip,
  Stack,
  Paper,
  Button,
  useTheme,
  CircularProgress,
  alpha,
  IconButton,
} from '@mui/material';
import { useProject } from '../../contexts/ProjectContext';
import ProjectDetail from '../ProjectDetail';
import LanguageIcon from '@mui/icons-material/Language';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import SettingsIcon from '@mui/icons-material/Settings';
import EditIcon from '@mui/icons-material/Edit';
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord';
import RefreshIcon from '@mui/icons-material/Refresh';

const ProjectInfoCard = ({ project }) => {
  const theme = useTheme();

  if (!project) return null;

  return (
    <Paper
      elevation={1}
      sx={{
        p: 3,
        mb: 3,
        backgroundColor: alpha(theme.palette.primary.main, 0.03),
        borderLeft: `4px solid ${theme.palette.primary.main}`,
        borderRadius: 1,
        position: 'relative',
      }}
    >
      <Grid container spacing={3}>
        <Grid item xs={12} sm={8}>
          <Typography variant='h5' fontWeight='500' gutterBottom>
            {project.title}
          </Typography>
          <Stack direction='row' spacing={1} alignItems='center' sx={{ mb: 2 }}>
            <Chip
              icon={<FiberManualRecordIcon sx={{ fontSize: '0.7rem !important' }} />}
              label={project.status}
              color={project.status === 'Active' ? 'success' : 'default'}
              size='small'
              sx={{
                borderRadius: '4px',
                '& .MuiChip-label': { fontWeight: 500 },
              }}
            />
            <Typography
              variant='body2'
              color='text.secondary'
              sx={{ display: 'flex', alignItems: 'center' }}
            >
              <Box
                component='span'
                sx={{
                  mx: 1,
                  width: '4px',
                  height: '4px',
                  borderRadius: '50%',
                  backgroundColor: 'text.disabled',
                }}
              ></Box>
              Project ID: {project.id}
            </Typography>
          </Stack>
        </Grid>

        <Grid
          item
          xs={12}
          sm={4}
          sx={{ display: 'flex', justifyContent: { xs: 'flex-start', sm: 'flex-end' } }}
        >
          <Button
            variant='outlined'
            startIcon={<EditIcon />}
            size='small'
            sx={{
              borderRadius: '4px',
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            Edit Project Info
          </Button>
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 1 }} />
        </Grid>

        <Grid item xs={12} md={6}>
          <Stack spacing={2}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                sx={{
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  borderRadius: '8px',
                  p: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <LanguageIcon color='primary' fontSize='small' />
              </Box>
              <Box>
                <Typography variant='subtitle2' color='text.secondary' fontWeight={500}>
                  Language
                </Typography>
                <Typography variant='body1' fontWeight={400}>
                  {project.language || 'Not specified'}
                </Typography>
              </Box>
            </Box>
          </Stack>
        </Grid>

        <Grid item xs={12} md={6}>
          <Stack spacing={2}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                sx={{
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  borderRadius: '8px',
                  p: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <LocationOnIcon color='primary' fontSize='small' />
              </Box>
              <Box>
                <Typography variant='subtitle2' color='text.secondary' fontWeight={500}>
                  Region
                </Typography>
                <Typography variant='body1' fontWeight={400}>
                  {project.region || 'Not specified'}
                </Typography>
              </Box>
            </Box>
          </Stack>
        </Grid>
      </Grid>
    </Paper>
  );
};

const Configuration = () => {
  const { selectedProject, loading, error, refreshProjects } = useProject();

  // Automatically retry loading projects if there's an error
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        refreshProjects();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [error, refreshProjects]);
  const theme = useTheme();

  return (
    <Box sx={{ maxWidth: 1200, margin: '0 auto', p: { xs: 2, md: 3 } }}>
      {/* Header Section with Background */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          backgroundColor: alpha(theme.palette.primary.main, 0.05),
          borderRadius: 2,
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: -20,
            right: -20,
            opacity: 0.1,
            transform: 'rotate(15deg)',
            zIndex: 0,
          }}
        >
          <SettingsIcon sx={{ fontSize: 160 }} />
        </Box>

        <Box sx={{ position: 'relative', zIndex: 1 }}>
          <Typography
            variant='h4'
            component='h1'
            gutterBottom
            sx={{
              fontWeight: 600,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <SettingsIcon sx={{ fontSize: 32, color: theme.palette.primary.main }} />
            Configuration
          </Typography>

          {/* Project Info Card */}
          {selectedProject && <ProjectInfoCard project={selectedProject} />}
        </Box>
      </Paper>

      {/* Main Content Card */}
      <Card
        elevation={2}
        sx={{
          mb: 4,
          borderRadius: 2,
          overflow: 'hidden',
        }}
      >
        {error && (
          <Box
            sx={{
              p: 3,
              backgroundColor: alpha(theme.palette.error.main, 0.05),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Typography color='error' sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Box
                component='span'
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  backgroundColor: theme.palette.error.main,
                  display: 'inline-block',
                }}
              ></Box>
              {error}
            </Typography>
            <IconButton
              color='primary'
              onClick={refreshProjects}
              disabled={loading}
              sx={{ ml: 2 }}
              aria-label='Refresh projects'
            >
              <RefreshIcon />
            </IconButton>
          </Box>
        )}

        {loading ? (
          <Box sx={{ p: 5, textAlign: 'center' }}>
            <CircularProgress size={40} thickness={4} />
            <Typography sx={{ mt: 2 }}>Loading project configuration...</Typography>
          </Box>
        ) : !selectedProject ? (
          <Box sx={{ p: 5, textAlign: 'center' }}>
            <Typography variant='h6' color='text.secondary' gutterBottom>
              No Project Selected
            </Typography>
            <Typography color='text.secondary'>
              Please select a project from the dropdown in the header to view its configuration.
            </Typography>
          </Box>
        ) : (
          <ProjectDetail projectId={selectedProject.id} />
        )}
      </Card>
    </Box>
  );
};

export default Configuration;
