import React, { useState, useEffect } from 'react';
import ResponsiveTabs from '../../components/Tabs/ResponsiveTabs';
import { Card, Box, Typography, Alert, alpha, useTheme } from '@mui/material';
import Personas from '../Personas';
import Prompts from '../Prompts';
import Questionnaires from '../Questionnaires';
import { useParams } from 'react-router-dom';
import { getProjectById } from '../../services/projectsService';
import { useProject } from '../../contexts/ProjectContext';
import PersonIcon from '@mui/icons-material/Person';
import ChatIcon from '@mui/icons-material/Chat';
import QuizIcon from '@mui/icons-material/Quiz';

const tabList = [
  {
    label: 'Personas',
    value: 'personas',
    icon: <PersonIcon fontSize='small' />,
    iconPosition: 'start',
  },
  {
    label: 'Prompts',
    value: 'prompts',
    icon: <ChatIcon fontSize='small' />,
    iconPosition: 'start',
  },
  {
    label: 'Questionnaire',
    value: 'questionnaire',
    icon: <QuizIcon fontSize='small' />,
    iconPosition: 'start',
  },
];

const ProjectDetail = () => {
  const theme = useTheme();
  const { setCurrentProject } = useProject();
  const [tab, setTab] = useState('personas');
  const [error, setError] = useState(null);
  const [project, setProject] = useState(null);
  const [loading, setLoading] = useState(true);
  const { id: urlProjectId } = useParams();
  const { selectedProjectId } = useProject();
  const projectId = urlProjectId || selectedProjectId;

  useEffect(() => {
    const fetchProject = async () => {
      if (!projectId) return;

      setLoading(true);
      setError(null);
      try {
        const response = await getProjectById(projectId);
        setCurrentProject(response.data);
        setError(null);
      } catch (err) {
        console.error('Error loading project:', err);
        setError('Failed to load project details');
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [projectId]);

  if (!projectId) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity='info'>
          Please select a project from the dropdown in the header to view its details.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ width: '100%', margin: '0 auto' }}>
      {/* Enhanced Tab Navigation */}
      <Box
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          mb: 3,
          position: 'relative',
          '&:after': {
            content: '""',
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: '1px',
            backgroundColor: theme.palette.divider,
          },
        }}
      >
        <ResponsiveTabs
          tabs={tabList}
          value={tabList.findIndex((t) => t.value === tab)}
          onChange={(_, value) => {
            setTab(tabList[value].value);
          }}
          variant='scrollable'
          scrollButtons='auto'
          allowScrollButtonsMobile
          tabColor='primary'
          sx={{
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '0.95rem',
              minHeight: 48,
              py: 1.5,
              '&.Mui-selected': {
                fontWeight: 600,
              },
            },
            '& .MuiTabs-indicator': {
              height: 3,
              borderTopLeftRadius: 3,
              borderTopRightRadius: 3,
            },
          }}
        />
      </Box>

      {/* Tab Content */}
      <Box>
        {tab === 'personas' && (
          <Card
            elevation={0}
            sx={{
              p: 0,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              borderRadius: 2,
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                p: 2,
                backgroundColor: alpha(theme.palette.primary.main, 0.03),
                borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <PersonIcon color='primary' fontSize='small' />
              <Typography variant='h6' fontWeight={500}>
                Persona List
              </Typography>
            </Box>
            <Box sx={{ p: 3 }}>
              <Personas projectId={projectId} inProjectDetail={true} setError={setError} />
            </Box>
          </Card>
        )}
        {tab === 'prompts' && (
          <Card
            elevation={0}
            sx={{
              p: 0,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              borderRadius: 2,
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                p: 2,
                backgroundColor: alpha(theme.palette.primary.main, 0.03),
                borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <ChatIcon color='primary' fontSize='small' />
              <Typography variant='h6' fontWeight={500}>
                Prompt List
              </Typography>
            </Box>
            <Box sx={{ p: 3 }}>
              <Prompts projectId={projectId} inProjectDetail={true} setError={setError} />
            </Box>
          </Card>
        )}
        {tab === 'questionnaire' && (
          <Card
            elevation={0}
            sx={{
              p: 0,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              borderRadius: 2,
              overflow: 'hidden',
            }}
          >
            <Box
              sx={{
                p: 2,
                backgroundColor: alpha(theme.palette.primary.main, 0.03),
                borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              <QuizIcon color='primary' fontSize='small' />
              <Typography variant='h6' fontWeight={500}>
                Questionnaire List
              </Typography>
            </Box>
            <Box sx={{ p: 3 }}>
              <Questionnaires projectId={projectId} inProjectDetail={true} setError={setError} />
            </Box>
          </Card>
        )}
      </Box>
    </Box>
  );
};

export default ProjectDetail;
