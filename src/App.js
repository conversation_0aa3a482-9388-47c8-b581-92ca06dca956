import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import theme from './theme';
import { AuthProvider } from './contexts/AuthContext';
import { ProjectProvider } from './contexts/ProjectContext';
import AuthGuard from './components/auth/AuthGuard';
import Layout from './components/Layout';
import LoginPage from './pages/Login';
import './App.css';

// Vegrow Voice UI pages (lazy loaded)
const Dashboard = lazy(() => import('./pages/Dashboard'));
const CallTrigger = lazy(() => import('./pages/CallTrigger'));
const CallLogs = lazy(() => import('./pages/CallLogs'));
const Analytics = lazy(() => import('./pages/Analytics'));
const Configuration = lazy(() => import('./pages/Configuration'));
const Projects = lazy(() => import('./pages/Projects'));

// Google OAuth client ID - replace with your actual client ID from Google Developer Console
const GOOGLE_CLIENT_ID =
  process.env.REACT_APP_GOOGLE_CLIENT_ID || '123456789-example.apps.googleusercontent.com';

function App() {
  return (
    <Router>
      <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
        <ThemeProvider theme={theme}>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <CssBaseline />
            <AuthProvider>
              <ProjectProvider>
                <Routes>
                  {/* Public routes */}
                  <Route path='/login' element={<LoginPage />} />

                  {/* Protected routes - wrapped in Layout */}
                  <Route
                    path='/dashboard'
                    element={
                      <AuthGuard>
                        <Layout>
                          <Suspense fallback={<div>Loading...</div>}>
                            <Dashboard />
                          </Suspense>
                        </Layout>
                      </AuthGuard>
                    }
                  />

                  <Route
                    path='/call-trigger'
                    element={
                      <AuthGuard>
                        <Layout>
                          <Suspense fallback={<div>Loading...</div>}>
                            <CallTrigger />
                          </Suspense>
                        </Layout>
                      </AuthGuard>
                    }
                  />
                  <Route
                    path='/call-logs'
                    element={
                      <AuthGuard>
                        <Layout>
                          <Suspense fallback={<div>Loading...</div>}>
                            <CallLogs />
                          </Suspense>
                        </Layout>
                      </AuthGuard>
                    }
                  />
                  <Route
                    path='/analytics'
                    element={
                      <AuthGuard>
                        <Layout>
                          <Suspense fallback={<div>Loading...</div>}>
                            <Analytics />
                          </Suspense>
                        </Layout>
                      </AuthGuard>
                    }
                  />
                  <Route
                    path='/configuration'
                    element={
                      <AuthGuard>
                        <Layout>
                          <Suspense fallback={<div>Loading...</div>}>
                            <Configuration />
                          </Suspense>
                        </Layout>
                      </AuthGuard>
                    }
                  />
                  <Route
                    path='/projects'
                    element={
                      <AuthGuard>
                        <Layout>
                          <Suspense fallback={<div>Loading...</div>}>
                            <Projects />
                          </Suspense>
                        </Layout>
                      </AuthGuard>
                    }
                  />

                  {/* Redirect to login if not authenticated */}
                  <Route path='/' element={<Navigate to='/dashboard' replace />} />

                  {/* Catch all route - redirect to dashboard */}
                  <Route path='*' element={<Navigate to='/dashboard' replace />} />
                </Routes>
              </ProjectProvider>
            </AuthProvider>
          </LocalizationProvider>
        </ThemeProvider>
      </GoogleOAuthProvider>
    </Router>
  );
}

export default App;
