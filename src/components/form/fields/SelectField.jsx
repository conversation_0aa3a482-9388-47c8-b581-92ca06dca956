import React from 'react';
import { FormControl, Select, MenuItem, FormHelperText } from '@mui/material';
import { useField } from 'formik';

/**
 * Select dropdown component for forms
 * @param {Object} props - Component properties
 * @returns {JSX.Element}
 */
const SelectField = ({ label, options, helperText, required, placeholder, ...props }) => {
  const [field, meta] = useField(props);
  const isError = Boolean(meta.touched && meta.error);
  // Always prioritize the placeholder if provided
  const displayPlaceholder = placeholder || (required ? `${label} *` : label);

  return (
    <FormControl fullWidth variant='outlined' error={isError} margin='normal'>
      <Select
        {...field}
        {...props}
        displayEmpty
        MenuProps={{
          PaperProps: {
            sx: {
              mt: 0.5,
              boxShadow: '0px 5px 15px rgba(0, 0, 0, 0.15)',
              '& .MuiMenuItem-root': {
                fontSize: '0.875rem',
                padding: '8px 16px',
              },
            },
          },
        }}
        renderValue={(selected) => {
          if (!selected || selected === '') {
            // Return placeholder text with consistent styling
            return (
              <span style={{ color: '#9e9e9e', fontSize: '0.875rem', fontWeight: 400 }}>
                {displayPlaceholder}
              </span>
            );
          }
          // Return selected value in normal text color
          const selectedOption = options.find((opt) => opt.value === selected);
          return <span>{selectedOption?.label || selected}</span>;
        }}
        sx={{
          '& .MuiSelect-select': {
            color: field.value ? 'rgba(0, 0, 0, 0.87)' : '#9e9e9e',
            padding: '8px 12px',
            height: '40px',
            display: 'flex',
            alignItems: 'center',
            fontSize: '0.875rem',
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: isError ? '#f44336' : 'rgba(0, 0, 0, 0.23)',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: isError ? '#f44336' : 'rgba(0, 0, 0, 0.23)',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: isError ? '#f44336' : 'rgba(0, 0, 0, 0.23)',
            borderWidth: '1px',
          },
        }}
      >
        <MenuItem value='' disabled>
          <em>{displayPlaceholder}</em>
        </MenuItem>
        {options.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
      {(isError || helperText) && (
        <FormHelperText
          sx={{
            marginLeft: 0,
            color: isError ? '#f44336' : '#9e9e9e',
            fontSize: '0.75rem',
            lineHeight: 1.5,
            mt: 0.5,
          }}
        >
          {isError ? meta.error : helperText}
        </FormHelperText>
      )}
    </FormControl>
  );
};

export default SelectField;
