import React, { useState } from 'react';
import {
  Box,
  Button,
  FormHelperText,
  Typography,
  IconButton,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  FormControl,
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import DeleteIcon from '@mui/icons-material/Delete';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import { useField, useFormikContext } from 'formik';

/**
 * File upload component for forms
 * @param {Object} props - Component properties
 * @returns {JSX.Element}
 */
const FileUploadField = ({
  label,
  helperText,
  required,
  accept = '*',
  multiple = false,
  maxFiles = 5,
  maxSize = 5242880, // 5MB
  ...props
}) => {
  const { setFieldValue } = useFormikContext();
  const [field, meta] = useField(props);
  const isError = Boolean(meta.touched && meta.error);
  const [fileList, setFileList] = useState([]);
  const [fileErrors, setFileErrors] = useState([]);

  // Handle file selection
  const handleFileChange = (event) => {
    const newFiles = Array.from(event.target.files);
    const errors = [];
    const validFiles = [];

    // Validate each file
    newFiles.forEach((file) => {
      // Check file size
      if (file.size > maxSize) {
        errors.push(`${file.name} exceeds the maximum size of ${maxSize / 1048576}MB`);
        return;
      }

      validFiles.push(file);
    });

    // Check maximum number of files
    if (multiple && validFiles.length + fileList.length > maxFiles) {
      errors.push(`Maximum of ${maxFiles} files allowed`);
      return;
    }

    setFileErrors(errors);

    if (errors.length === 0) {
      if (multiple) {
        const updatedFiles = [...fileList, ...validFiles];
        setFileList(updatedFiles);
        setFieldValue(field.name, updatedFiles);
      } else {
        setFileList(validFiles);
        setFieldValue(field.name, validFiles[0]);
      }
    }
  };

  // Handle file removal
  const handleRemoveFile = (index) => {
    const updatedFiles = [...fileList];
    updatedFiles.splice(index, 1);
    setFileList(updatedFiles);

    if (multiple) {
      setFieldValue(field.name, updatedFiles);
    } else {
      setFieldValue(field.name, null);
    }
  };

  // Format file size for display
  const formatFileSize = (bytes) => {
    if (bytes < 1024) return bytes + ' bytes';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  return (
    <FormControl fullWidth error={isError} margin='normal'>
      <Typography variant='subtitle1' gutterBottom>
        {required ? `${label} *` : label}
      </Typography>

      <Paper variant='outlined' sx={{ p: 2, mb: 2 }}>
        <Box
          sx={{
            p: 3,
            border: '2px dashed #cccccc',
            borderRadius: 1,
            textAlign: 'center',
            mb: fileList.length > 0 ? 2 : 0,
          }}
        >
          <input
            id={props.id || props.name}
            name={props.name}
            type='file'
            accept={accept}
            multiple={multiple}
            onChange={handleFileChange}
            style={{ display: 'none' }}
          />
          <label htmlFor={props.id || props.name}>
            <CloudUploadIcon sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
            <Typography variant='body1' gutterBottom>
              Drag & drop files here, or click to select files
            </Typography>
            <Typography variant='caption' color='textSecondary' display='block'>
              {multiple
                ? `You can upload up to ${maxFiles} files (max ${maxSize / 1048576}MB each)`
                : `Maximum file size: ${maxSize / 1048576}MB`}
            </Typography>
            <Button
              variant='contained'
              component='span'
              startIcon={<AttachFileIcon />}
              sx={{ mt: 2 }}
            >
              Browse Files
            </Button>
          </label>
        </Box>

        {fileList.length > 0 && (
          <List>
            {fileList.map((file, index) => (
              <ListItem
                key={index}
                secondaryAction={
                  <IconButton
                    edge='end'
                    onClick={() => handleRemoveFile(index)}
                    aria-label='delete'
                  >
                    <DeleteIcon />
                  </IconButton>
                }
              >
                <ListItemIcon>
                  <AttachFileIcon />
                </ListItemIcon>
                <ListItemText primary={file.name} secondary={formatFileSize(file.size)} />
              </ListItem>
            ))}
          </List>
        )}
      </Paper>

      {fileErrors.length > 0 && (
        <FormHelperText error>
          {fileErrors.map((error, index) => (
            <div key={index}>{error}</div>
          ))}
        </FormHelperText>
      )}

      {isError && <FormHelperText error>{meta.error}</FormHelperText>}

      {!isError && !fileErrors.length && helperText && (
        <FormHelperText>{helperText}</FormHelperText>
      )}
    </FormControl>
  );
};

export default FileUploadField;
