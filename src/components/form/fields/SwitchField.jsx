import React from 'react';
import { useField } from 'formik';
import { FormControlLabel, Switch, FormHelperText, FormControl } from '@mui/material';

/**
 * SwitchField component for Formik forms
 *
 * @param {Object} props - Component props
 * @returns {JSX.Element}
 */
const SwitchField = ({ label, helperText, ...props }) => {
  const [field, meta, helpers] = useField({ ...props, type: 'checkbox' });

  const handleChange = (event) => {
    helpers.setValue(event.target.checked);
  };

  const isError = meta.touched && !!meta.error;

  return (
    <FormControl error={isError} fullWidth margin={props.margin || 'normal'}>
      <FormControlLabel
        control={
          <Switch checked={field.value} onChange={handleChange} color='primary' {...props} />
        }
        label={label}
      />
      {(helperText || isError) && (
        <FormHelperText>{isError ? meta.error : helperText}</FormHelperText>
      )}
    </FormControl>
  );
};

export default SwitchField;
