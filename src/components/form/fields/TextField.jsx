import React from 'react';
import { TextField as MuiTextField } from '@mui/material';
import { useField } from 'formik';

/**
 * TextField component for forms
 * @param {Object} props - Component properties
 * @returns {JSX.Element}
 */
const TextField = ({ label, helperText, required, placeholder, ...props }) => {
  const [field, meta] = useField(props);
  const isError = Boolean(meta.touched && meta.error);
  const displayPlaceholder = placeholder || (required ? `${label} *` : label);

  return (
    <MuiTextField
      {...field}
      {...props}
      label='' // Empty label to avoid conflicts with placeholder
      error={isError}
      helperText={null}
      fullWidth
      placeholder={displayPlaceholder}
      variant='outlined'
      margin='normal'
      InputLabelProps={{
        shrink: false,
        sx: { display: 'none' },
      }}
      InputProps={{
        sx: {
          '& .MuiInputBase-input': {
            color: field.value ? 'rgba(0, 0, 0, 0.87)' : '#9e9e9e',
            padding: '8px 12px',
            height: '40px',
            '&::placeholder': {
              opacity: 1,
              color: '#9e9e9e',
              fontSize: '0.875rem',
              fontWeight: 400,
            },
          },
          '& .MuiOutlinedInput-notchedOutline': {
            borderColor: isError ? '#f44336' : 'rgba(0, 0, 0, 0.23)',
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: isError ? '#f44336' : 'rgba(0, 0, 0, 0.23)',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderColor: isError ? '#f44336' : 'rgba(0, 0, 0, 0.23)',
            borderWidth: '1px',
          },
        },
      }}
    />
  );
};

export default TextField;
