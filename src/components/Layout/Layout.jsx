import React, { useState, useEffect } from 'react';
import { Box, CssBaseline, useMediaQuery, useTheme } from '@mui/material';
import AppHeader from './AppHeader';
import Sidebar from './Sidebar';

/**
 * Main application layout component that wraps all pages except login
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components to render inside layout
 * @returns {JSX.Element} Layout component
 */
const Layout = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [open, setOpen] = useState(!isMobile);

  // Effect to handle screen size changes
  useEffect(() => {
    setOpen(!isMobile);
  }, [isMobile]);

  // Toggle drawer open/close
  const handleDrawerToggle = () => {
    setOpen(!open);
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh' }}>
      <CssBaseline />

      {/* Header/AppBar with hamburger menu */}
      <AppHeader open={open} handleDrawerToggle={handleDrawerToggle} />

      <Box sx={{ display: 'flex', width: '100%' }}>
        {/* Collapsible sidebar navigation */}
        <Box
          sx={{
            width: open ? 240 : 0,
            flexShrink: 0,
            transition: theme.transitions.create('width', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
            '& .MuiDrawer-paper': {
              width: 240,
              boxSizing: 'border-box',
              position: 'relative',
              height: 'calc(100vh - 64px)',
              [theme.breakpoints.down('sm')]: {
                height: 'calc(100vh - 56px)',
              },
            },
          }}
        >
          <Sidebar
            open={open}
            handleDrawerToggle={handleDrawerToggle}
            variant={isMobile ? 'temporary' : 'persistent'}
          />
        </Box>

        {/* Main content area */}
        <Box
          component='main'
          sx={{
            flexGrow: 1,
            p: 3,
            mt: 8, // Add margin to accommodate app bar
            overflow: 'auto',
            width: open ? 'calc(100% - 240px)' : '100%',
            transition: theme.transitions.create(['width', 'margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
          }}
        >
          {children}
        </Box>
      </Box>
    </Box>
  );
};

export default Layout;
