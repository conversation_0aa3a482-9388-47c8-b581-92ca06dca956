import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Box,
  Typography,
  styled,
  useTheme,
  alpha,
} from '@mui/material';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import {
  Dashboard as DashboardIcon,
  Phone as PhoneIcon,
  History as HistoryIcon,
  Business as BusinessIcon,
} from '@mui/icons-material';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import AnalyticsIcon from '@mui/icons-material/Analytics';
import SettingsIcon from '@mui/icons-material/Settings';

// Styled component for the drawer
// const StyledDrawer = styled(Drawer)(({ theme, variant }) => ({
//   width: 240,
//   flexShrink: 0,
//   '& .MuiDrawer-paper': {
//     width: 240,
//     boxSizing: 'border-box',
//     top: 64, // Height of AppBar
//     height: 'calc(100% - 64px)',
//     [theme.breakpoints.down('sm')]: {
//       top: 56, // AppBar height on mobile
//       height: 'calc(100% - 56px)',
//     },
//   },
//   // Make temporary drawer different from permanent
//   ...(variant === 'temporary' && {
//     '& .MuiDrawer-paper': {
//       position: 'fixed',
//       zIndex: theme.zIndex.drawer,
//     },
//   }),
// }));

// Styled component for active menu items
const ActiveListItemButton = styled(ListItemButton)(({ theme, active }) => ({
  backgroundColor: active ? alpha(theme.palette.primary.main, 0.08) : 'transparent',
  borderRadius: '8px',
  margin: '4px 8px',
  padding: '8px 16px',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.04),
  },
  '& .MuiListItemIcon-root': {
    color: active ? theme.palette.primary.main : theme.palette.text.secondary,
  },
  '& .MuiListItemText-primary': {
    fontWeight: active ? 600 : 400,
    fontSize: '0.9rem',
    color: active ? theme.palette.primary.main : theme.palette.text.primary,
  },
}));

/**
 * Navigation menu structure with nested options
 * This can be expanded as needed with more menu options
 */
// Define menu items for navigation

const menuItems = [
  {
    id: 'dashboard',
    title: 'Dashboard',
    path: '/dashboard',
    icon: <DashboardIcon fontSize='small' />,
  },
  {
    id: 'projects',
    title: 'Projects',
    path: '/projects',
    icon: <BusinessIcon fontSize='small' />,
  },
  {
    id: 'call-trigger',
    title: 'Call Trigger',
    path: '/call-trigger',
    icon: <PhoneIcon fontSize='small' />,
  },
  {
    id: 'call-logs',
    title: 'Call Logs',
    path: '/call-logs',
    icon: <HistoryIcon fontSize='small' />,
  },
  {
    id: 'analytics',
    title: 'Analytics',
    path: '/analytics',
    icon: <AnalyticsIcon fontSize='small' />,
  },
  {
    id: 'configuration',
    title: 'Configuration',
    path: '/configuration',
    icon: <SettingsIcon fontSize='small' />,
  },
];

/**
 * Sidebar component with navigation menu
 * Collapsible drawer that can be toggled with hamburger menu
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether sidebar is open
 * @param {Function} props.handleDrawerToggle - Function to toggle sidebar
 * @param {string} props.variant - Drawer variant (temporary|persistent|permanent)
 * @returns {JSX.Element} Sidebar component
 */
const Sidebar = ({ open, handleDrawerToggle, variant = 'persistent' }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedMenus, setExpandedMenus] = useState({});

  // Check if a path is active
  const isActivePath = (path) => {
    return location.pathname === path;
  };

  // Check if a parent menu has an active child
  const hasActiveChild = (children) => {
    return children?.some((child) => isActivePath(child.path));
  };

  // Toggle menu expansion
  const handleToggleMenu = (id) => {
    setExpandedMenus((prev) => ({
      ...prev,
      [id]: !prev[id],
    }));
  };

  // Set up event handler for list item clicks (to close drawer on mobile)
  const handleMenuItemClick = (path) => {
    try {
      navigate(path);
      if (variant === 'temporary') {
        handleDrawerToggle(); // Close drawer on mobile when item is clicked
      }
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  // Recursive function to render menu items
  const renderMenuItem = (item) => {
    const hasChildren = item.children && item.children.length > 0;
    const isActive = isActivePath(item.path);
    const isExpanded = expandedMenus[item.id] || hasActiveChild(item.children);

    return (
      <React.Fragment key={item.id || item.path}>
        <ListItem disablePadding sx={{ display: 'block' }}>
          <ActiveListItemButton
            active={isActive ? 1 : 0}
            component={item.path && !hasChildren ? Link : undefined}
            to={item.path && !hasChildren ? item.path : undefined}
            onClick={() => {
              if (hasChildren) {
                handleToggleMenu(item.id);
              } else if (item.path) {
                // Use handleMenuItemClick instead of handleNavigate to properly close drawer on mobile
                handleMenuItemClick(item.path);
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 36 }}>{item.icon}</ListItemIcon>
            <ListItemText primary={item.title} />
            {hasChildren &&
              (isExpanded ? <ExpandLess fontSize='small' /> : <ExpandMore fontSize='small' />)}
          </ActiveListItemButton>
        </ListItem>

        {hasChildren && (
          <Collapse in={isExpanded} timeout='auto' unmountOnExit>
            <List component='div' disablePadding>
              {item.children.map((child) => (
                <ListItem disablePadding key={child.id}>
                  <ActiveListItemButton
                    active={isActivePath(child.path) ? 1 : 0}
                    onClick={() => handleMenuItemClick(child.path)}
                    component={Link}
                    to={child.path}
                    sx={{ pl: 4 }}
                  >
                    <ListItemText primary={child.title} />
                  </ActiveListItemButton>
                </ListItem>
              ))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const drawerContent = (
    <>
      <List component='nav' sx={{ px: 1 }}>
        {menuItems.map(renderMenuItem)}
      </List>
      {/* This empty box will push the white background to full height */}
      <Box sx={{ flexGrow: 1, bgcolor: 'background.paper', minHeight: '70vh' }} />
    </>
  );

  const drawerProps =
    variant === 'temporary'
      ? {
          variant: 'temporary',
          anchor: 'left',
          open: open,
          onClose: handleDrawerToggle,
          ModalProps: {
            keepMounted: true,
          },
          sx: {
            '& .MuiDrawer-paper': {
              boxSizing: 'border-box',
              width: 240,
              boxShadow: 3,
            },
          },
        }
      : {
          variant: 'persistent',
          anchor: 'left',
          open: open,
          sx: {
            width: 240,
            flexShrink: 0,
            [`& .MuiDrawer-paper`]: {
              width: 240,
              boxSizing: 'border-box',
            },
          },
        };

  return (
    <Drawer
      {...drawerProps}
      sx={{
        width: 240,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 240,
          boxSizing: 'border-box',
          top: 64, // AppBar height
          height: 'calc(100% - 64px)',
          borderRight: `1px solid ${alpha(theme.palette.divider, 0.7)}`,
          backgroundColor: alpha(theme.palette.background.paper, 0.98),
          [theme.breakpoints.down('sm')]: {
            top: 56, // AppBar height on mobile
            height: 'calc(100% - 56px)',
          },
        },
      }}
    >
      {drawerContent}
    </Drawer>
  );
};

export default Sidebar;
