import React from 'react';
import {
  AppB<PERSON>,
  Toolbar,
  Typography,
  IconButton,
  Box,
  Avatar,
  Menu,
  MenuItem,
  Tooltip,
  Divider,
} from '@mui/material';
import MenuIcon from '@mui/icons-material/Menu';
import { useAuth } from '../../contexts/AuthContext';
import { useProject } from '../../contexts/ProjectContext';
import { useState } from 'react';

/**
 * Application header component with hamburger menu and user profile
 * @param {Object} props - Component props
 * @param {boolean} props.open - Whether sidebar is open
 * @param {Function} props.handleDrawerToggle - Function to toggle sidebar
 * @returns {JSX.Element} AppHeader component
 */
const AppHeader = ({ open, handleDrawerToggle }) => {
  const { user, logout } = useAuth();
  const { projects, selectedProjectId, setSelectedProjectId, refreshProjects } = useProject();
  const [anchorEl, setAnchorEl] = useState(null);

  const handleMenu = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleClose();
    logout();
  };

  // Handle project selection
  const handleProjectSelect = (projectId) => {
    setSelectedProjectId(projectId);
    // Refresh data when project changes
    refreshProjects();
    handleClose();
  };

  return (
    <AppBar
      position='fixed'
      sx={{
        zIndex: (theme) => theme.zIndex.drawer + 1,
        boxShadow: 1,
        backgroundColor: 'white',
      }}
    >
      <Toolbar>
        <IconButton
          color='inherit'
          aria-label='toggle drawer'
          edge='start'
          onClick={handleDrawerToggle}
          sx={{ mr: 2, color: 'primary.main' }}
        >
          <MenuIcon />
        </IconButton>

        {/* Left section - App name */}
        <Typography variant='h6' component='div' sx={{ fontSize: '1.1rem', color: 'primary.main' }}>
          Vegrow Voice
        </Typography>

        {/* Center section - Selected project name */}
        <Box sx={{ flexGrow: 1, display: 'flex', justifyContent: 'center' }}>
          <Typography
            variant='h6'
            component='div'
            sx={{
              fontSize: '1.1rem',
              fontWeight: 500,
              color: '#000',
            }}
          >
            {projects.find((project) => project.id === selectedProjectId)?.title ||
              'Select a Project'}
          </Typography>
        </Box>

        {user && (
          <Box>
            <Tooltip title='Account settings'>
              <IconButton onClick={handleMenu} sx={{ p: 0 }}>
                <Avatar
                  alt={user.name || 'User'}
                  src={user.picture}
                  sx={{ width: 40, height: 40 }}
                />
              </IconButton>
            </Tooltip>

            <Menu
              id='menu-appbar'
              anchorEl={anchorEl}
              keepMounted
              open={Boolean(anchorEl)}
              onClose={handleClose}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
            >
              <MenuItem>
                <Typography>{user.name}</Typography>
              </MenuItem>
              <MenuItem>
                <Typography variant='body2' color='textSecondary'>
                  {user.email}
                </Typography>
              </MenuItem>
              <Divider />
              <MenuItem>
                <Typography variant='body2' color='textSecondary'>
                  Switch Project
                </Typography>
              </MenuItem>
              <Box sx={{ maxHeight: '200px', overflowY: 'auto' }}>
                {projects.map((project) => (
                  <MenuItem
                    key={project.id}
                    onClick={() => handleProjectSelect(project.id)}
                    sx={{
                      backgroundColor:
                        selectedProjectId === project.id
                          ? 'rgba(0, 150, 136, 0.08)'
                          : 'transparent',
                      '&:hover': {
                        backgroundColor:
                          selectedProjectId === project.id
                            ? 'rgba(0, 150, 136, 0.12)'
                            : 'rgba(0, 0, 0, 0.04)',
                      },
                    }}
                  >
                    {project.title}
                    {selectedProjectId === project.id && (
                      <Box component='span' sx={{ ml: 1, color: 'primary.main' }}>
                        ✓
                      </Box>
                    )}
                  </MenuItem>
                ))}
              </Box>
              <Divider />
              <MenuItem onClick={handleLogout}>Logout</MenuItem>
            </Menu>
          </Box>
        )}
      </Toolbar>
    </AppBar>
  );
};

export default AppHeader;
