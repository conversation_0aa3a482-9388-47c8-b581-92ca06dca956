import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Typography,
  Box,
  Divider,
} from '@mui/material';

/**
 * Modal for creating or editing a version for prompts, personas, or questionnaires
 *
 * This component is designed to handle versioning for different entity types
 * with appropriate fields and validation for each type.
 *
 * @param {Object} props
 * @param {boolean} props.open - Whether the dialog is open
 * @param {Function} props.onClose - Function to call when dialog is closed
 * @param {Function} props.onSubmit - Function to call when form is submitted
 * @param {Object} props.initialValues - Initial values for the form
 * @param {string} props.mode - 'create' or 'edit'
 * @param {Array} props.fields - Configuration for form fields
 * @param {string} props.entityType - Type of entity ('prompt', 'persona', 'questionnaire')
 */
const VersionFormModal = ({
  open,
  onClose,
  onSubmit,
  initialValues = {},
  mode = 'create',
  fields = [],
  entityType,
}) => {
  const safeInitialValues = initialValues || {};

  const [values, setValues] = useState(safeInitialValues);
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (open) {
      setValues(safeInitialValues);
      setErrors({});
    }
  }, [safeInitialValues, open]);

  const handleChange = (e) => {
    const { name, value, checked, type } = e.target;
    setValues({
      ...values,
      [name]: type === 'checkbox' ? checked : value,
    });

    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };

  const validate = () => {
    const newErrors = {};

    fields.forEach((field) => {
      if (field.required && !values[field.name]) {
        newErrors[field.name] = `${field.label} is required`;
      }
    });

    if (!values.change_notes) {
      newErrors.change_notes = 'Change notes are required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validate()) return;

    const safeValues = { ...values };

    if (safeValues.change_notes === null || safeValues.change_notes === undefined) {
      safeValues.change_notes = '';
    }

    fields.forEach((field) => {
      if (
        field.required &&
        (safeValues[field.name] === null || safeValues[field.name] === undefined)
      ) {
        if (field.type === 'select' && field.options && field.options.length > 0) {
          safeValues[field.name] = field.options[0].value;
        } else if (field.type === 'switch') {
          safeValues[field.name] = false;
        } else {
          safeValues[field.name] = '';
        }
      }
    });

    setIsSubmitting(true);
    try {
      await onSubmit(safeValues);
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getEntityTitle = () => {
    const capitalizedEntity = entityType.charAt(0).toUpperCase() + entityType.slice(1);
    return mode === 'create'
      ? `Add New ${capitalizedEntity} Version`
      : `Edit ${capitalizedEntity} Version`;
  };

  const shouldShowField = (fieldName) => {
    if (['change_notes', 'status'].includes(fieldName)) {
      return true;
    }

    switch (entityType) {
      case 'prompt':
        return ['prompt_text', 'model', 'type'].includes(fieldName);
      case 'persona':
        return ['voice_type', 'engine', 'language', 'from_number'].includes(fieldName);
      case 'questionnaire':
        return ['question_text', 'question_type', 'options', 'order_no'].includes(fieldName);
      default:
        return true;
    }
  };

  const renderField = (field) => {
    if (!shouldShowField(field.name)) {
      return null;
    }

    if (values == null) {
      console.warn(`Values object is null or undefined when rendering field ${field.name}`);
      return null;
    }

    const safeValue = values[field.name] == null ? '' : values[field.name];

    const fieldLabel = field.required ? `${field.label} *` : field.label;

    switch (field.type) {
      case 'select':
        return (
          <Box key={field.name} sx={{ mb: 2 }}>
            <Typography variant='body2' sx={{ mb: 1 }}>
              {fieldLabel}
            </Typography>
            <FormControl fullWidth variant='outlined' error={!!errors[field.name]}>
              <Select name={field.name} value={safeValue} onChange={handleChange} displayEmpty>
                <MenuItem value='' disabled>
                  <em>Select {field.label}</em>
                </MenuItem>
                {field.options?.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
              {errors[field.name] && (
                <Typography variant='caption' color='error'>
                  {errors[field.name]}
                </Typography>
              )}
            </FormControl>
          </Box>
        );

      case 'switch':
        return (
          <Box key={field.name} sx={{ mb: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  name={field.name}
                  checked={!!safeValue}
                  onChange={handleChange}
                  color='primary'
                />
              }
              label={fieldLabel}
            />
            {errors[field.name] && (
              <Typography variant='caption' color='error' display='block'>
                {errors[field.name]}
              </Typography>
            )}
          </Box>
        );

      case 'textarea':
        return (
          <Box key={field.name} sx={{ mb: 2 }}>
            <Typography variant='body2' sx={{ mb: 1 }}>
              {fieldLabel}
            </Typography>
            <TextField
              name={field.name}
              value={safeValue}
              onChange={handleChange}
              error={!!errors[field.name]}
              helperText={errors[field.name]}
              fullWidth
              multiline
              rows={4}
              placeholder={`Enter ${field.label}`}
              variant='outlined'
            />
          </Box>
        );

      default:
        return (
          <Box key={field.name} sx={{ mb: 2 }}>
            <Typography variant='body2' sx={{ mb: 1 }}>
              {fieldLabel}
            </Typography>
            <TextField
              name={field.name}
              value={safeValue}
              onChange={handleChange}
              error={!!errors[field.name]}
              helperText={errors[field.name]}
              fullWidth
              placeholder={`Enter ${field.label}`}
              type={field.type || 'text'}
              variant='outlined'
            />
          </Box>
        );
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth='md' fullWidth>
      <DialogTitle>{getEntityTitle()}</DialogTitle>
      <form onSubmit={handleSubmit}>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Typography variant='subtitle1' fontWeight='bold' sx={{ mt: 1 }}>
              {entityType.charAt(0).toUpperCase() + entityType.slice(1)} Details
            </Typography>
            <Divider sx={{ mb: 2 }} />

            {fields.map((field) => renderField(field))}

            <Typography variant='subtitle1' fontWeight='bold' sx={{ mt: 2 }}>
              Version Information
            </Typography>
            <Divider sx={{ mb: 2 }} />

            <Box>
              <Typography variant='body2' sx={{ mb: 1 }}>
                Change Notes *
              </Typography>
              <TextField
                name='change_notes'
                value={values.change_notes || ''}
                onChange={handleChange}
                error={!!errors.change_notes}
                helperText={errors.change_notes}
                fullWidth
                multiline
                rows={3}
                placeholder='Describe the changes made in this version'
                variant='outlined'
              />
            </Box>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button type='submit' variant='contained' disabled={isSubmitting} color='primary'>
            {mode === 'create' ? 'Create' : 'Save'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default VersionFormModal;
