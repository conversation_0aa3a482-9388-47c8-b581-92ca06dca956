import React from 'react';
import PropTypes from 'prop-types';
import { Card, CardContent, Typography, Box, Avatar, styled, useTheme } from '@mui/material';

/**
 * A reusable card component for displaying statistics or summary information
 * Can be customized with different colors, icons, and content
 *
 * @component
 */
const StatCard = ({
  title,
  value,
  icon,
  valueColor,
  customColor,
  subtitle,
  onClick,
  width,
  minHeight = 120,
  variant = 'default',
}) => {
  const theme = useTheme();

  // Determine color for the value based on variant or customColor
  const getValueColor = () => {
    if (valueColor) return valueColor;

    if (customColor) return customColor;

    switch (variant) {
      case 'success':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'error':
        return theme.palette.error.main;
      case 'info':
        return theme.palette.info.main;
      default:
        return theme.palette.text.primary;
    }
  };

  // Apply styling based on whether card is clickable
  const cardStyles = {
    width,
    minHeight,
    height: '100%',
    transition: 'all 0.3s ease',
    ...(onClick && {
      cursor: 'pointer',
      '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: theme.shadows[4],
      },
    }),
  };

  const StyledCard = styled(Card)(({ theme }) => ({
    height: '100%',
    display: 'flex',
    position: 'relative',
    overflow: 'hidden',
    borderRadius: theme.shape.borderRadius,
    boxShadow: theme.shadows[2],
    '&:hover': {
      boxShadow: theme.shadows[6],
      transform: 'translateY(-4px)',
      transition: 'all 0.3s ease',
    },
  }));

  const IconWrapper = styled(Avatar)(({ theme, bgcolor }) => ({
    backgroundColor: bgcolor || theme.palette.primary.main,
    color: theme.palette.common.white,
    width: 64,
    height: 64,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    right: -12,
    top: -12,
    borderRadius: '50%',
    opacity: 0.8,
  }));

  return (
    <StyledCard sx={cardStyles}>
      <CardContent sx={{ width: '100%', pt: 3, pb: 3 }}>
        <IconWrapper bgcolor={customColor}>{icon}</IconWrapper>

        <Box sx={{ ml: 1 }}>
          <Typography
            variant='h4'
            component='div'
            sx={{ fontWeight: 'bold', mb: 1, color: getValueColor() }}
          >
            {value}
          </Typography>
          <Typography variant='body2' color='text.secondary'>
            {title}
          </Typography>
        </Box>
      </CardContent>
    </StyledCard>
  );
};

StatCard.propTypes = {
  /** Card title displayed at the top */
  title: PropTypes.string.isRequired,
  /** Main value/statistic to display */
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
  /** Optional icon to display next to the value */
  icon: PropTypes.node,
  /** Color for the value text (overrides variant) */
  valueColor: PropTypes.string,
  /** Custom color for the card */
  customColor: PropTypes.string,
  /** Optional subtitle displayed below the value */
  subtitle: PropTypes.node,
  /** Click handler for the card */
  onClick: PropTypes.func,
  /** Width of the card */
  width: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  /** Minimum height of the card */
  minHeight: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  /** Predefined color variants */
  variant: PropTypes.oneOf(['default', 'success', 'warning', 'error', 'info']),
};

export default StatCard;
