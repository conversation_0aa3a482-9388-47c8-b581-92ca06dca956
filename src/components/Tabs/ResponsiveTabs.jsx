import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Tabs, Tab, Box, useTheme, useMediaQuery, Menu, MenuItem, Button } from '@mui/material';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

/**
 * ResponsiveTabs component that adapts to screen size
 * @param {Object} props
 * @returns {JSX.Element}
 */
const ResponsiveTabs = ({
  tabs,
  value,
  onChange,
  ariaLabel = 'tabs',
  tabColor = 'primary',
  variant = 'standard',
  orientation = 'horizontal',
  centered = false,
  scrollButtons = 'auto',
  selectionFollowsFocus = false,
  allowScrollButtonsMobile = false,
  sx,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleMenuItemClick = (index) => {
    onChange(null, index);
    handleClose();
  };

  // For very small screens, render a dropdown
  if (isMobile && tabs.length > 3) {
    const selectedTab = tabs[value] || tabs[0];
    return (
      <Box sx={{ mb: 2 }}>
        <Button
          endIcon={<KeyboardArrowDownIcon />}
          onClick={handleClick}
          variant='outlined'
          fullWidth
          sx={{
            justifyContent: 'space-between',
            textTransform: 'none',
            fontWeight: 500,
            py: 1,
          }}
        >
          {selectedTab.icon && (
            <Box component='span' sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>
              {selectedTab.icon}
            </Box>
          )}
          {selectedTab.label}
        </Button>
        <Menu
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          MenuListProps={{
            'aria-labelledby': 'tab-button',
          }}
          sx={{ mt: 1 }}
        >
          {tabs.map((tab, index) => (
            <MenuItem
              key={index}
              selected={index === value}
              onClick={() => handleMenuItemClick(index)}
              disabled={tab.disabled}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
              }}
            >
              {tab.icon && (
                <Box component='span' sx={{ display: 'flex', alignItems: 'center' }}>
                  {tab.icon}
                </Box>
              )}
              {tab.label}
            </MenuItem>
          ))}
        </Menu>
      </Box>
    );
  }

  // For larger screens or fewer tabs, render regular tabs
  return (
    <Box>
      <Tabs
        value={value}
        onChange={onChange}
        aria-label={ariaLabel}
        variant={tabs.length > 6 ? 'scrollable' : variant}
        scrollButtons={scrollButtons}
        allowScrollButtonsMobile={allowScrollButtonsMobile}
        orientation={orientation}
        centered={centered}
        selectionFollowsFocus={selectionFollowsFocus}
        textColor={tabColor}
        indicatorColor={tabColor}
        sx={{
          ...sx,
          '& .MuiTab-root': {
            minHeight: 48,
            padding: '12px 16px',
            textTransform: 'none',
            fontWeight: 500,
            fontSize: '0.95rem',
            ...(sx?.['& .MuiTab-root'] || {}),
          },
          '& .MuiTabs-indicator': {
            height: 3,
            borderTopLeftRadius: 3,
            borderTopRightRadius: 3,
          },
        }}
      >
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            label={tab.label}
            icon={tab.icon}
            iconPosition={tab.iconPosition || 'top'}
            disabled={tab.disabled}
          />
        ))}
      </Tabs>
    </Box>
  );
};

ResponsiveTabs.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      icon: PropTypes.node,
      disabled: PropTypes.bool,
      iconPosition: PropTypes.oneOf(['start', 'end', 'top', 'bottom']),
    })
  ).isRequired,
  value: PropTypes.number.isRequired,
  onChange: PropTypes.func.isRequired,
  ariaLabel: PropTypes.string,
  tabColor: PropTypes.oneOf(['primary', 'secondary', 'inherit']),
  variant: PropTypes.oneOf(['standard', 'scrollable', 'fullWidth']),
  orientation: PropTypes.oneOf(['horizontal', 'vertical']),
  /** Whether tabs should be centered (only applies if variant is "standard") */
  centered: PropTypes.bool,
  /** When to show scroll buttons */
  scrollButtons: PropTypes.oneOf(['auto', 'desktop', 'never', true, false]),
  /** If true, pressing arrow keys changes focus without changing selected tab */
  selectionFollowsFocus: PropTypes.bool,
  /** If true, scroll buttons are shown on mobile */
  allowScrollButtonsMobile: PropTypes.bool,
};

export default ResponsiveTabs;
