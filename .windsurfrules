This is a React application for the VEGROW Voice UI.


## Tech Stack

Keep on updating docs as per the changes in the code.
Docs are basically for reference of developers on how to make changes in the code and give them information about business flows


Keep the modularity of the code in mind while making changes
Reuse the components as much as possible 
Datatable component is already created in src/components/DataTable/DataTable.jsx
StatCard component is already created in src/components/StatCard/StatCard.jsx

For api calls use the api service instance created in src/services/base/index.js




